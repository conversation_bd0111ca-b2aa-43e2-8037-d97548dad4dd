/* Role Custom Admin Para <PERSON> */

.role-custom-admin-withdraw {
    max-width: 1200px;
}

/* <PERSON>rum Filtreleri */
.subsubsub {
    margin: 20px 0;
    padding: 0;
    font-size: 13px;
}

.subsubsub a {
    text-decoration: none;
    color: #0073aa;
}

.subsubsub a.current {
    font-weight: 600;
    color: #000;
}

.subsubsub .count {
    color: #999;
}

/* Para Talepleri Tablosu */
.withdraw-requests-table {
    margin-top: 20px;
}

.withdraw-requests-table table {
    width: 100%;
    border-collapse: collapse;
}

.withdraw-requests-table th,
.withdraw-requests-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    vertical-align: top;
}

.withdraw-requests-table th {
    background-color: #f9f9f9;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
}

.withdraw-requests-table tr:hover {
    background-color: #f5f5f5;
}

/* Durum Badge'leri */
.withdraw-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.withdraw-status.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.withdraw-status.approved {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.withdraw-status.rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.withdraw-status.unknown {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* İşlem Butonları */
.approve-withdraw {
    background-color: #00a32a !important;
    border-color: #00a32a !important;
    color: white !important;
    margin-right: 5px;
}

.approve-withdraw:hover {
    background-color: #008a20 !important;
    border-color: #008a20 !important;
}

.reject-withdraw {
    background-color: #d63638 !important;
    border-color: #d63638 !important;
    color: white !important;
}

.reject-withdraw:hover {
    background-color: #b32d2e !important;
    border-color: #b32d2e !important;
}

.delete-withdraw {
    color: #a00 !important;
    text-decoration: none;
    border: none;
    background: none;
    cursor: pointer;
    margin-left: 10px;
    font-size: 12px;
    padding: 4px 8px;
}

.delete-withdraw:hover {
    color: #dc3232 !important;
    text-decoration: underline;
}

/* Loading Durumu */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Boş Durum */
.no-requests {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.no-requests p {
    margin: 0;
    font-size: 16px;
}

/* Hesap Bilgileri */
.withdraw-requests-table td small {
    color: #666;
    font-size: 12px;
}

.withdraw-requests-table td strong {
    color: #1d2327;
}

/* Responsive */
@media (max-width: 768px) {
    .withdraw-requests-table {
        overflow-x: auto;
    }
    
    .withdraw-requests-table table {
        min-width: 800px;
    }
    
    .withdraw-requests-table th,
    .withdraw-requests-table td {
        padding: 8px;
        font-size: 13px;
    }
    
    .approve-withdraw,
    .reject-withdraw {
        display: block;
        width: 100%;
        margin: 2px 0;
        font-size: 12px;
        padding: 4px 8px;
    }
}

/* Başarı ve Hata Mesajları */
.role-custom-admin-notice {
    padding: 12px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.role-custom-admin-notice.success {
    background-color: #d1edff;
    border-left-color: #00a32a;
    color: #00a32a;
}

.role-custom-admin-notice.error {
    background-color: #f8d7da;
    border-left-color: #d63638;
    color: #d63638;
}

/* Tablo Sıralama */
.withdraw-requests-table th.sortable {
    cursor: pointer;
    position: relative;
}

.withdraw-requests-table th.sortable:hover {
    background-color: #f0f0f1;
}

.withdraw-requests-table th.sortable::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #999;
}

.withdraw-requests-table th.sortable.desc::after {
    border-bottom: none;
    border-top: 4px solid #999;
}

/* Miktar Vurgusu */
.withdraw-requests-table .amount {
    font-weight: 600;
    color: #2271b1;
    font-size: 14px;
}

/* Tarih Formatı */
.withdraw-requests-table .date {
    color: #666;
    font-size: 13px;
}

/* Kullanıcı Bilgileri */
.withdraw-requests-table .user-info strong {
    display: block;
    margin-bottom: 2px;
}

.withdraw-requests-table .user-info small {
    color: #666;
}

/* Hesap Detayları */
.withdraw-requests-table .account-details {
    font-size: 12px;
    line-height: 1.4;
}

.withdraw-requests-table .account-details strong {
    display: block;
    margin-bottom: 2px;
    color: #1d2327;
}

/* Not Satırı */
.withdraw-requests-table .note-row {
    background-color: #f9f9f9;
    font-style: italic;
}

.withdraw-requests-table .note-row td {
    padding: 8px 12px;
    border-top: none;
    color: #666;
}

/* İşlem Tamamlandı Durumu */
.withdraw-requests-table .completed {
    color: #666;
    font-style: italic;
    font-size: 12px;
}
